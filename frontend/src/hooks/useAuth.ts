/**
 * Authentication Hook
 * Manages authentication state and provides auth methods
 */

import { useState, useEffect, useCallback } from 'react';
import { authService, LoginRequest, User } from '../services';

interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuth = useCallback(async (): Promise<boolean> => {
    try {
      if (!authService.isAuthenticated()) {
        setUser(null);
        setIsLoading(false);
        return false;
      }

      const storedUser = authService.getUser();
      if (storedUser) {
        // Verify token with server
        const isValid = await authService.verifyToken();
        if (isValid) {
          setUser(storedUser);
          setIsLoading(false);
          return true;
        }
      }

      // Token invalid, clear auth
      authService.logout();
      setUser(null);
      setIsLoading(false);
      return false;
    } catch (error) {
      console.error('Auth check failed:', error);
      authService.logout();
      setUser(null);
      setIsLoading(false);
      return false;
    }
  }, []);

  const login = useCallback(async (credentials: LoginRequest): Promise<void> => {
    setIsLoading(true);
    try {
      const loginResponse = await authService.login(credentials);
      const userData: User = {
        id: loginResponse.id,
        username: loginResponse.username,
        role: loginResponse.role,
        tenant_id: loginResponse.tenant_id,
        tenant_label: loginResponse.tenant_label,
      };
      setUser(userData);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback((): void => {
    authService.logout();
    setUser(null);
  }, []);

  // Check authentication on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    checkAuth,
  };
};
