/**
 * Login Page
 * Handles login logic and state management
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';
import LoginForm from '../components/auth.loginForm';

const LoginPage: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [clientId, setClientId] = useState('ambition-guru');
  const [error, setError] = useState<string | null>(null);

  const { login, isLoading, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as { from?: { pathname?: string } })?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!username.trim() || !password.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      await login({
        username: username.trim(),
        password: password.trim(),
        client_id: clientId.trim() || 'ambition-guru',
      });

      // Navigation will be handled by useEffect when isAuthenticated changes
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Login failed. Please try again.');
    }
  };

  const handleSignupClick = () => {
    navigate('/auth/signup');
  };

  // Don't render if already authenticated (prevents flash)
  if (isAuthenticated) {
    return null;
  }

  return (
    <LoginForm
      username={username}
      password={password}
      clientId={clientId}
      isLoading={isLoading}
      error={error}
      onUsernameChange={setUsername}
      onPasswordChange={setPassword}
      onClientIdChange={setClientId}
      onSubmit={handleSubmit}
      onSignupClick={handleSignupClick}
    />
  );
};

export default LoginPage;
