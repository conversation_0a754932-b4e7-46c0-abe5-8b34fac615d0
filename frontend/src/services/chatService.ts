/**
 * Chat Service
 * Handles chat API communication and message management
 */

import { http } from './baseHttp';

// Types
export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: Date;
  thread_id: string;
  user_id: string;
}

export interface ChatRequest {
  message: string;
}

export interface ChatResponse {
  response: string;
  thread_id: string;
  user_id: string;
}

/**
 * Chat Service Class
 */
class ChatService {
  private readonly CHAT_HISTORY_KEY = 'chat_history';

  /**
   * Send a chat message to the API
   */
  async sendMessage(message: string): Promise<ChatResponse> {
    try {
      const response = await http.post<ChatResponse>('/api/v1/chat', {
        message,
      });

      // Store message in local history
      this.addToHistory({
        id: Date.now().toString(),
        message,
        response: response.data.response,
        timestamp: new Date(),
        thread_id: response.data.thread_id,
        user_id: response.data.user_id,
      });

      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to send message');
    }
  }

  /**
   * Get chat history from local storage
   */
  getChatHistory(): ChatMessage[] {
    const historyStr = localStorage.getItem(this.CHAT_HISTORY_KEY);
    if (!historyStr) return [];

    try {
      const history = JSON.parse(historyStr);
      return history.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
      }));
    } catch {
      return [];
    }
  }

  /**
   * Add message to chat history
   */
  private addToHistory(message: ChatMessage): void {
    const history = this.getChatHistory();
    history.push(message);

    // Keep only last 100 messages
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    localStorage.setItem(this.CHAT_HISTORY_KEY, JSON.stringify(history));
  }

  /**
   * Clear chat history
   */
  clearHistory(): void {
    localStorage.removeItem(this.CHAT_HISTORY_KEY);
  }

  /**
   * Check chat service health
   */
  async checkHealth(): Promise<boolean> {
    try {
      await http.get('/api/v1/chat/health');
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const chatService = new ChatService();
export default chatService;
