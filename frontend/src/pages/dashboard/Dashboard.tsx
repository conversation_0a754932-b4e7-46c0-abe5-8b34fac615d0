/**
 * Dashboard Page
 * Main dashboard with dummy data and statistics
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Users, 
  TrendingUp, 
  Calendar,
  MessageCircle,
  Award,
  Clock,
  Target
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: string;
  trend?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    className={`bg-white rounded-xl shadow-lg p-6 border-l-4 ${color}`}
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
        {trend && (
          <p className="text-sm text-green-600 flex items-center mt-1">
            <TrendingUp className="h-4 w-4 mr-1" />
            {trend}
          </p>
        )}
      </div>
      <div className={`p-3 rounded-full bg-opacity-10 ${color.replace('border-l-', 'bg-')}`}>
        {icon}
      </div>
    </div>
  </motion.div>
);

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const stats = [
    {
      title: 'Total Courses',
      value: '13',
      icon: <BookOpen className="h-6 w-6 text-blue-600" />,
      color: 'border-l-blue-500',
      trend: '+2 this month'
    },
    {
      title: 'Active Students',
      value: '1,247',
      icon: <Users className="h-6 w-6 text-green-600" />,
      color: 'border-l-green-500',
      trend: '+15% this week'
    },
    {
      title: 'Completion Rate',
      value: '87%',
      icon: <Award className="h-6 w-6 text-purple-600" />,
      color: 'border-l-purple-500',
      trend: '+5% improvement'
    },
    {
      title: 'Chat Sessions',
      value: '342',
      icon: <MessageCircle className="h-6 w-6 text-orange-600" />,
      color: 'border-l-orange-500',
      trend: '+23 today'
    }
  ];

  const recentCourses = [
    { name: 'IELTS Preparation', students: 89, status: 'Active' },
    { name: 'Korean Language - TOPIK Level 1', students: 67, status: 'Active' },
    { name: 'SEE Bridge Course', students: 156, status: 'Active' },
    { name: 'German Language - A1 Level', students: 43, status: 'Active' },
    { name: 'CSIT Entrance Prep', students: 78, status: 'Active' }
  ];

  const recentActivities = [
    { action: 'New student enrolled in IELTS Preparation', time: '2 minutes ago' },
    { action: 'Chat session completed for Korean course inquiry', time: '15 minutes ago' },
    { action: 'Course completion certificate issued', time: '1 hour ago' },
    { action: 'New booking for German A1 Level', time: '2 hours ago' },
    { action: 'Student feedback received for SEE Bridge', time: '3 hours ago' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white shadow-sm border-b"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {user?.username || 'User'}!
              </h1>
              <p className="text-gray-600">
                Here's what's happening with your courses today.
              </p>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Calendar className="h-4 w-4" />
              <span>{new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</span>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.1 }}
            >
              <StatCard {...stat} />
            </motion.div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Courses */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Popular Courses</h2>
              <Target className="h-5 w-5 text-gray-400" />
            </div>
            <div className="space-y-4">
              {recentCourses.map((course, index) => (
                <motion.div
                  key={course.name}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div>
                    <p className="font-medium text-gray-900">{course.name}</p>
                    <p className="text-sm text-gray-600">{course.students} students</p>
                  </div>
                  <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                    {course.status}
                  </span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Recent Activities */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-lg p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Recent Activities</h2>
              <Clock className="h-5 w-5 text-gray-400" />
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
