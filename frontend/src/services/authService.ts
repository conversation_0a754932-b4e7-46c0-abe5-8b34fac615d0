/**
 * Authentication Service
 * Handles login, signup, token management, and user authentication
 */

import { http } from './baseHttp';

// Types
export interface LoginRequest {
  username: string;
  password: string;
  client_id: string;
}

export interface LoginResponse {
  id: string;
  access_token: string;
  token_type: string;
  username: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
  tenant_slug: string;
  nav_permission: Record<string, boolean>;
  token_validity: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    total_seconds: number;
  };
  expires_at: string;
}

export interface SignupRequest {
  username: string;
  password: string;
  email: string;
  role?: string;
}

export interface User {
  id: string;
  username: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
}

/**
 * Authentication Service Class
 */
class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'user_info';

  /**
   * Login user with credentials
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // Convert to form data as expected by the API
      const formData = new FormData();
      formData.append('username', credentials.username);
      formData.append('password', credentials.password);
      formData.append('client_id', credentials.client_id);

      const response = await http.post<LoginResponse>('/api/v1/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const loginData = response.data;

      // Store token and user info
      this.setToken(loginData.access_token);
      this.setUser({
        id: loginData.id,
        username: loginData.username,
        role: loginData.role,
        tenant_id: loginData.tenant_id,
        tenant_label: loginData.tenant_label,
      });

      return loginData;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Login failed');
    }
  }

  /**
   * Register new user (if signup endpoint exists)
   */
  async signup(userData: SignupRequest): Promise<void> {
    try {
      await http.post('/api/v1/register', userData);
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Signup failed');
    }
  }

  /**
   * Logout user
   */
  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    // Check if token is expired (basic check)
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Set token in storage
   */
  private setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * Get stored user info
   */
  getUser(): User | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  /**
   * Set user info in storage
   */
  private setUser(user: User): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  /**
   * Verify token with server
   */
  async verifyToken(): Promise<boolean> {
    try {
      await http.get('/api/v1/verify_token');
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
