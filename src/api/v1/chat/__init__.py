from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional
import sys
import os

from api.services.agent_v2.main_agent import MainAgentV2 as MainAgent
from core.security import get_tenant_info
from models.user import UserTenantDB
from utils import setup_colored_logging, log_info, log_error, log_success

# Setup logging
setup_colored_logging()

router = APIRouter(tags=["Chat"])

# Initialize the main agent globally to avoid re-initialization
main_agent: Optional[MainAgent] = None

def get_main_agent() -> MainAgent:
    """Get or initialize the main agent"""
    global main_agent
    if main_agent is None:
        try:
            log_info("Initializing Main Agent for chat...")
            main_agent = MainAgent()
            log_success("Main Agent initialized successfully")
        except Exception as e:
            log_error("Failed to initialize Main Agent", e)
            raise HTTPException(
                status_code=500,
                detail="Failed to initialize chat agent"
            )
    return main_agent


class ChatRequest(BaseModel):
    message: str


class ToolUsed(BaseModel):
    name: str
    description: str
    input: dict = {}
    output: str = ""

class ChatResponse(BaseModel):
    response: str
    thread_id: str
    user_id: str
    tools_used: list[ToolUsed] = []


@router.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Chat endpoint that uses current agent with user's thread ID
    The user ID from the token is used as the thread ID for conversation continuity
    """
    try:
        # Get the main agent
        agent = get_main_agent()
        
        # Use user ID as thread ID for conversation continuity per user
        thread_id = str(current_user.user.id)
        user_id = str(current_user.user.id)
        
        log_info(f"Chat request from user {current_user.user.username} (ID: {user_id}): {chat_request.message[:50]}...")
        
        # Get response from main agent using user ID as thread ID
        agent_response = agent.chat(chat_request.message, thread_id)

        log_success(f"Chat response generated for user {current_user.user.username}")

        # Handle both old string format and new dict format for backward compatibility
        if isinstance(agent_response, dict):
            response_text = agent_response.get("response", "")
            tools_used = [ToolUsed(**tool) for tool in agent_response.get("tools_used", [])]
        else:
            response_text = agent_response
            tools_used = []

        return ChatResponse(
            response=response_text,
            thread_id=thread_id,
            user_id=user_id,
            tools_used=tools_used
        )
        
    except Exception as e:
        log_error(f"Chat failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to process chat request"
        )


@router.get("/chat/health")
async def chat_health_check():
    """
    Health check endpoint for chat service
    """
    try:
        agent = get_main_agent()
        return {
            "status": "healthy",
            "agent_initialized": agent is not None,
            "service": "chat"
        }
    except Exception as e:
        log_error("Chat health check failed", e)
        return {
            "status": "unhealthy",
            "agent_initialized": False,
            "service": "chat",
            "error": str(e)
        }
