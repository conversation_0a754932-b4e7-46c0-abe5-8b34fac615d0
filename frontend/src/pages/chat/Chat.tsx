/**
 * Chat Page - Playground Interface
 * Modern chat interface with code display panel
 */

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageCircle,
  Trash2,
  Code2,
  Co<PERSON>,
  ChevronRight,
  ChevronDown,
  Play,
  Settings
} from 'lucide-react';
import { useChat } from '../../hooks/useChat';
import { useAuth } from '../../hooks/useAuth';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import { Button } from '../../components';

const Chat: React.FC = () => {
  const { messages, isLoading, error, sendMessage, clearHistory, isTyping } = useChat();
  const { user } = useAuth();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [showCodePanel, setShowCodePanel] = useState(true);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const handleSendMessage = async (message: string) => {
    await sendMessage(message);
  };

  const handleClearHistory = () => {
    if (window.confirm('Are you sure you want to clear all chat history?')) {
      clearHistory();
    }
  };

  const handleExportChat = () => {
    const chatData = messages.map(msg => ({
      timestamp: msg.timestamp.toISOString(),
      user_message: msg.message,
      bot_response: msg.response,
    }));

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
  };

  // Mock data for tools used (this would come from your backend)
  const getToolsForMessage = (messageId: string) => {
    return [
      {
        name: "search_courses",
        description: "Search for available courses",
        code: `def search_courses(query: str):
    """Search for courses matching the query"""
    if query.lower() in ["python", "programming"]:
        return [
            {"name": "Python Basics", "duration": "4 weeks"},
            {"name": "Advanced Python", "duration": "6 weeks"}
        ]
    return []`,
        language: "python"
      },
      {
        name: "book_appointment",
        description: "Book a consultation appointment",
        code: `def book_appointment(date: str, time: str):
    """Book an appointment for the given date and time"""
    return {
        "status": "confirmed",
        "appointment_id": "APT-001",
        "date": date,
        "time": time
    }`,
        language: "python"
      }
    ];
  };

  return (
    <div className="h-full flex bg-gray-50">
      {/* Chat Panel */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Chat Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <MessageCircle className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Nobi AI v2.0</h1>
                <p className="text-sm text-gray-500">AI Assistant</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearHistory}
                disabled={messages.length === 0}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCodePanel(!showCodePanel)}
              >
                <Code2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Start a conversation
              </h3>
              <p className="text-gray-500 text-sm">
                Ask me anything about courses, programming, or get help with your questions.
              </p>
            </div>
          ) : (
            messages.map((msg) => (
              <div key={msg.id} className="space-y-4">
                {/* User Message */}
                <div className="flex justify-end">
                  <div className="max-w-xs lg:max-w-md">
                    <div className="bg-purple-600 text-white px-4 py-2 rounded-2xl rounded-br-md">
                      <p className="text-sm">{msg.message}</p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 text-right">
                      {msg.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>

                {/* Bot Response */}
                {msg.response && (
                  <div className="flex justify-start">
                    <div className="flex items-start space-x-3 max-w-xs lg:max-w-md">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-xs font-medium">AI</span>
                      </div>
                      <div
                        className="bg-white border border-gray-200 px-4 py-2 rounded-2xl rounded-bl-md cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => setSelectedMessage(selectedMessage === msg.id ? null : msg.id)}
                      >
                        <p className="text-sm text-gray-800">{msg.response}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {msg.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium">AI</span>
                </div>
                <div className="bg-white border border-gray-200 px-4 py-2 rounded-2xl rounded-bl-md">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input */}
        <div className="px-4 py-4 border-t border-gray-200">
          <ChatInput
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            placeholder="Type your question or request here..."
          />
        </div>
      </div>

      {/* Code/Tools Panel */}
      {showCodePanel && (
        <div className="w-96 bg-gray-50 border-l border-gray-200 flex flex-col">
          <div className="px-4 py-3 border-b border-gray-200 bg-white">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Tools Used</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCodePanel(false)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {selectedMessage ? (
              <div className="space-y-4">
                {getToolsForMessage(selectedMessage).map((tool, index) => (
                  <div key={index} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">{tool.name}</h4>
                          <p className="text-xs text-gray-500">{tool.description}</p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4">
                      <pre className="text-xs text-gray-800 bg-gray-50 p-3 rounded border overflow-x-auto">
                        <code>{tool.code}</code>
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Code2 className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                <p className="text-sm text-gray-500">
                  Click on a message to see the tools used
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;
