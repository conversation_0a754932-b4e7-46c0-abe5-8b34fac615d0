/**
 * Chat Message Component
 * Animated chat message bubble with typing effects
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, <PERSON><PERSON>, Copy, Check } from 'lucide-react';

interface ChatMessageProps {
  message: string;
  response?: string;
  timestamp: Date;
  isUser: boolean;
  isTyping?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  response,
  timestamp,
  isUser,
  isTyping = false,
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const [copied, setCopied] = useState(false);

  const textToDisplay = isUser ? message : response || '';

  // Typing animation effect for bot messages
  useEffect(() => {
    if (!isUser && textToDisplay && !isComplete) {
      setDisplayedText('');
      let index = 0;
      const timer = setInterval(() => {
        if (index < textToDisplay.length) {
          setDisplayedText(textToDisplay.slice(0, index + 1));
          index++;
        } else {
          setIsComplete(true);
          clearInterval(timer);
        }
      }, 30); // Typing speed

      return () => clearInterval(timer);
    } else if (isUser) {
      setDisplayedText(textToDisplay);
      setIsComplete(true);
    }
  }, [textToDisplay, isUser, isComplete]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(textToDisplay);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const messageVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
  };

  const bubbleVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: { duration: 0.2, ease: 'easeOut' }
    },
  };

  return (
    <motion.div
      variants={messageVariants}
      initial="hidden"
      animate="visible"
      className={`flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}
    >
      {/* Avatar */}
      <motion.div
        variants={bubbleVariants}
        className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser 
            ? 'bg-blue-600 text-white' 
            : 'bg-gray-200 text-gray-600'
        }`}
      >
        {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
      </motion.div>

      {/* Message Bubble */}
      <motion.div
        variants={bubbleVariants}
        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl relative group ${
          isUser
            ? 'bg-blue-600 text-white rounded-br-md'
            : 'bg-white text-gray-800 shadow-md border rounded-bl-md'
        }`}
      >
        {/* Message Content */}
        <div className="text-sm leading-relaxed">
          {isTyping && !isUser ? (
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="flex space-x-1"
            >
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            </motion.div>
          ) : (
            <div className="whitespace-pre-wrap">
              {displayedText}
              {!isComplete && !isUser && (
                <motion.span
                  animate={{ opacity: [0, 1, 0] }}
                  transition={{ duration: 0.8, repeat: Infinity }}
                  className="inline-block w-0.5 h-4 bg-current ml-0.5"
                />
              )}
            </div>
          )}
        </div>

        {/* Copy Button */}
        {isComplete && textToDisplay && (
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            onClick={handleCopy}
            className={`absolute top-1 right-1 p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity ${
              isUser 
                ? 'text-blue-200 hover:text-white hover:bg-blue-700' 
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
            }`}
          >
            {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
          </motion.button>
        )}

        {/* Timestamp */}
        <div className={`text-xs mt-1 ${
          isUser ? 'text-blue-200' : 'text-gray-500'
        }`}>
          {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ChatMessage;
