/**
 * Services Barrel Export
 * Centralized export for all services
 */

export { default as httpClient, http } from './baseHttp';
export { default as authService } from './authService';
export { default as chatService } from './chatService';

// Re-export types
export type {
  LoginRequest,
  LoginResponse,
  SignupRequest,
  User,
} from './authService';

export type {
  ChatMessage,
  ChatRequest,
  ChatResponse,
} from './chatService';
