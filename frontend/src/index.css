@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  border-color: #e5e7eb;
}

body {
  background-color: white;
  color: #111827;
  font-feature-settings: "rlig" 1, "calt" 1;
  margin: 0;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

@layer components {
  /* Custom component styles */
  .chat-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl relative group;
  }

  .chat-bubble-user {
    @apply bg-blue-600 text-white rounded-br-md;
  }

  .chat-bubble-bot {
    @apply bg-white text-gray-800 shadow-md border rounded-bl-md;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }

  .animate-typing {
    animation: typing 1.5s infinite;
  }

  @keyframes typing {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
}
