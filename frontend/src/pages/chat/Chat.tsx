/**
 * Chat Page
 * Main chat interface with animated messages and real-time communication
 */

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  Trash2, 
  Download, 
  Settings,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { useChat } from '../../hooks/useChat';
import { useAuth } from '../../hooks/useAuth';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import { Button } from '../../components';

const Chat: React.FC = () => {
  const { messages, isLoading, error, sendMessage, clearHistory, isTyping } = useChat();
  const { user } = useAuth();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isMinimized, setIsMinimized] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const handleSendMessage = async (message: string) => {
    await sendMessage(message);
  };

  const handleClearHistory = () => {
    if (window.confirm('Are you sure you want to clear all chat history?')) {
      clearHistory();
    }
  };

  const handleExportChat = () => {
    const chatData = messages.map(msg => ({
      timestamp: msg.timestamp.toISOString(),
      user_message: msg.message,
      bot_response: msg.response,
    }));

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col"
    >
      {/* Header */}
      <motion.div
        variants={headerVariants}
        className="bg-white shadow-sm border-b px-6 py-4"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 10 }}
              className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center"
            >
              <MessageCircle className="h-5 w-5 text-white" />
            </motion.div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Course Assistant
              </h1>
              <p className="text-sm text-gray-600">
                Hello {user?.username}! How can I help you today?
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
            >
              {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExportChat}
              disabled={messages.length === 0}
            >
              <Download className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearHistory}
              disabled={messages.length === 0}
            >
              <Trash2 className="h-4 w-4" />
            </Button>

            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Chat Messages Area */}
      <div className={`flex-1 overflow-hidden ${isMinimized ? 'h-0' : ''}`}>
        <div className="h-full flex flex-col">
          <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
            <AnimatePresence mode="popLayout">
              {messages.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-12"
                >
                  <motion.div
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                    className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <MessageCircle className="h-8 w-8 text-blue-600" />
                  </motion.div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Start a conversation
                  </h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    Ask me about our courses, book a class, or get help with any questions you have!
                  </p>
                </motion.div>
              ) : (
                messages.map((msg, index) => (
                  <React.Fragment key={msg.id}>
                    {/* User Message */}
                    <ChatMessage
                      message={msg.message}
                      timestamp={msg.timestamp}
                      isUser={true}
                    />
                    
                    {/* Bot Response */}
                    {msg.response && (
                      <ChatMessage
                        message=""
                        response={msg.response}
                        timestamp={msg.timestamp}
                        isUser={false}
                      />
                    )}
                  </React.Fragment>
                ))
              )}

              {/* Typing Indicator */}
              {isTyping && (
                <ChatMessage
                  message=""
                  timestamp={new Date()}
                  isUser={false}
                  isTyping={true}
                />
              )}
            </AnimatePresence>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mx-auto max-w-md"
              >
                <p className="text-sm">{error}</p>
              </motion.div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Chat Input */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="px-6 py-4 bg-white border-t"
          >
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              placeholder="Ask about courses, book a class, or get help..."
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default Chat;
