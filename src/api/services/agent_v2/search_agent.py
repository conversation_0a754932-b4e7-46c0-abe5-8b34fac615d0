"""
Search Agent V2 - Modern LangChain implementation with proper memory management
Uses LangGraph's built-in memory patterns instead of hardcoded context handling
"""

import logging
from typing import Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON><PERSON>ptTemplate
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph
import os
from dotenv import load_dotenv

# from config import get_vector_store_manager

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """
    Search Agent V2 - Dynamic search using LLM instead of hardcoded data
    """

    def __init__(self, tenant_id: str = "ambition-guru"):
        """Initialize dynamic search agent"""
        self.tenant_id = tenant_id

        # Initialize LLM for dynamic search
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.3,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )

        # Create memory for conversation context
        self.memory = MemorySaver()

        # Dynamic knowledge base - this would be loaded from database/API in real implementation
        self.knowledge_base = {
            "courses": """Available courses and programs:
- SEE Bridge Course: Preparation for Grade 10 to +2 transition
- BBS/BBA Programs: Business administration courses
- CSIT Preparation: Computer Science and IT entrance prep
- IELTS Preparation: English language proficiency for international study
- Korean Language (TOPIK): Korean language certification
- German Language: A1, B1 levels for study/work abroad
- Various entrance exam preparations
""",
            "general_info": """General information about our services:
- Online and offline classes available
- Expert instructors with years of experience
- Flexible payment plans
- Official certificates recognized in Nepal
- Support for university entrance exams
- Language courses for international migration
""",
            "troubleshooting": """Common troubleshooting help:
- App installation and setup issues
- Login and account problems
- Payment and booking issues
- Technical support for online classes
- Course material access problems
"""
        }
            {
                "name": "SEE Bridge Course",
                "code": "SEE-BRIDGE",
                "description": "Intensive course to prepare students transitioning from Grade 10 (SEE) to +2 level."
            },
            {
                "name": "BBS 1st Semester",
                "code": "BBS-SEM1",
                "description": "Bachelor of Business Studies first semester course under Tribhuvan University."
            },
            {
                "name": "BBA 1st Semester",
                "code": "BBA-SEM1",
                "description": "Bachelor of Business Administration first semester course for management students."
            },
            {
                "name": "CSIT Entrance Prep",
                "code": "CSIT-ENT",
                "description": "Preparation course for BSc CSIT entrance exam under Tribhuvan University."
            },
            {
                "name": "IELTS Preparation",
                "code": "IELTS-PREP",
                "description": "English proficiency test preparation course for international study or migration."
            },
            {
                "name": "Korean Language - TOPIK Level 1",
                "code": "KOR-L1",
                "description": "Beginner-level Korean language course targeting TOPIK Level 1 exam."
            },
            {
                "name": "Korean Language - TOPIK Level 2",
                "code": "KOR-L2",
                "description": "Intermediate Korean language course targeting TOPIK Level 2 for EPS Korea."
            },
            {
                "name": "German Language - A1 Level",
                "code": "GER-A1",
                "description": "Basic German language course aligned with CEFR A1 level for study/work abroad."
            },
            {
                "name": "German Language - B1 Level",
                "code": "GER-B1",
                "description": "Intermediate German language course (CEFR B1) for higher-level proficiency."
            },
            {
                "name": "EIS Grade 11",
                "code": "EIS-G11",
                "description": "Education in Science Grade 11 curriculum focused on core science subjects."
            },
            {
                "name": "WWIS Grade 12",
                "code": "WWIS-G12",
                "description": "World Wide Information Science Grade 12 content for IT and general knowledge."
            },
            {
                "name": "NASU Level 1 Preparation",
                "code": "NASU-L1",
                "description": "Preparation for NASU Level 1 government exam including general knowledge and aptitude."
            },
            {
                "name": "NIMABI Exam Prep",
                "code": "NIMABI-PREP",
                "description": "Training for the Nepal Madarsa Board (NIMABI) exams for Islamic education."
            }
        ]

        self.support_info = [
            "We provide comprehensive support for all Nepali students",
            "Classes available both online and offline in Kathmandu",
            "All courses include official certificates recognized in Nepal",
            "Flexible payment plans available for students",
            "Expert instructors with years of experience",
            "Special preparation for TU, KU, and other university exams",
            "Language courses certified for international migration",
            "Study materials provided in both Nepali and English"
        ]

        logger.info("✅ Search Agent V2 initialized with mock data for testing")
    
    def _setup_query_translator(self):
        """Setup LangGraph workflow for intelligent query translation - DISABLED FOR TESTING"""
        pass
        # Commented out for testing - using simple mock responses instead
        # # Define the state schema for query translation
        # workflow = StateGraph(state_schema=MessagesState)
        
        def translate_query(state: MessagesState):
            """Translate user query based on conversation context"""
            
            # Get conversation history from state
            messages = state["messages"]
            current_message = messages[-1].content if messages else ""
            
            # Create context from recent messages
            context_messages = messages[-5:] if len(messages) > 1 else []
            context_text = " ".join([msg.content for msg in context_messages[:-1]])
            
            # System prompt for query translation
            system_prompt = """You are a query translator that converts user messages into effective search queries.

Based on the conversation context and current message, determine:
1. The best search query to use
2. Whether this is an "information" search (troubleshooting, apps, services) or "products" search (courses, programs)

Context analysis rules:
- If previous messages mention problems ("chalena", "not working", "issue", "error"), treat as troubleshooting
- If user mentions specific apps like "Ambition Guru" with problem context, focus on that app's troubleshooting
- If asking about courses without problem context, treat as product search
- If asking "what is" about an app mentioned before, provide app information

Current message: {current_message}
Recent conversation context: {context}

Respond with:
1. Translated query (optimized for search)
2. Search type: "information" or "products"
3. Brief reasoning

Format: 
Query: [your translated query]
Type: [information/products]
Reasoning: [brief explanation]"""

            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                ("human", "Translate this query: {current_message}\nContext: {context}")
            ])
            
            # Get translation
            response = self.llm.invoke(
                prompt.format_messages(
                    current_message=current_message,
                    context=context_text
                )
            )
            
            return {"messages": [response]}
        
        # Add node and compile
        workflow.add_node("translate", translate_query)
        workflow.add_edge(START, "translate")
        
        self.query_translator = workflow.compile(checkpointer=self.memory)
    
    def _translate_query_with_context(self, user_message: str, search_type: str, thread_id: str = "default") -> tuple[str, str]:
        """
        Translate user message using LangGraph memory for context awareness
        
        Args:
            user_message: Raw user input
            search_type: Intended search type
            thread_id: Thread ID for memory context
            
        Returns:
            Tuple of (translated_query, corrected_search_type)
        """
        # Simple mock implementation for testing
        logger.info(f"🔄 Mock query translation: '{user_message}' (type: {search_type})")
        return user_message, search_type
    
    def _simple_query_translation(self, user_message: str, search_type: str) -> tuple[str, str]:
        """Fallback simple query translation without context"""
        user_lower = user_message.lower()
        
        # Problem detection
        if any(word in user_lower for word in ["chalena", "not working", "problem", "issue", "error"]):
            if "ambition guru" in user_lower or "ambition" in user_lower:
                return f"Ambition Guru app troubleshooting problems not working", "information"
            else:
                return f"troubleshooting problems not working {user_message}", "information"
        
        # Course queries
        if search_type == "products" and any(word in user_lower for word in ["course", "kors", "kun kun"]):
            return f"available courses programs {user_message}", "products"
        
        # Default
        return f"{search_type} {user_message}", search_type

    def _search_with_retriever(self, search_query: str, search_type: str) -> str:
        """
        DISABLED - Using mock data instead
        """
        # This method is disabled for testing - using mock responses in search methods
        return "Mock search disabled"

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Search for general information using mock data

        Args:
            user_message: The user's original message
            thread_id: Thread ID for memory context

        Returns:
            Formatted search results
        """
        logger.info(f"📋 Information search: '{user_message}'")

        # Return support information
        info_results = "\n".join([f"• {info}" for info in self.support_info])

        return f"""Here's some helpful information about our services:

{info_results}

Is there anything specific you'd like to know more about?"""
    
    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Search for products using mock data with intelligent matching

        Args:
            user_message: The user's original message
            thread_id: Thread ID for memory context

        Returns:
            Formatted search results
        """
        logger.info(f"🎓 Products search: '{user_message}'")

        user_lower = user_message.lower()

        # Check if user is asking about a specific course
        specific_course_found = None
        matched_courses = []

        # Search for specific course mentions
        for course in self.real_courses:
            course_name = course["name"]
            course_lower = course_name.lower()
            course_code = course["code"].lower()

            # Check for exact matches or partial matches in name or code
            search_words = user_lower.split()
            if any(word in course_lower or word in course_code for word in search_words if len(word) > 2):
                matched_courses.append(course)
                if not specific_course_found:
                    specific_course_found = course

        # If user is asking about a specific course that exists
        if specific_course_found and any(word in user_lower for word in ["book", "enroll", "register", "take", "join"]):
            course = specific_course_found
            return f"""✅ Great choice! "{course['name']}" is available in our course catalog.

📚 Course Details:
• **Course Name**: {course['name']}
• **Course Code**: {course['code']}
• **Description**: {course['description']}

✨ What's included:
- Certificate upon completion
- Expert instruction
- Comprehensive study materials
- Exam preparation support

Would you like to proceed with booking this course? I can help you get started right away!"""

        # If specific courses were found but not booking intent
        elif matched_courses:
            course_results = "\n".join([f"• **{course['name']}** ({course['code']}) - {course['description']}" for course in matched_courses])
            return f"""Here are the courses matching your search:

{course_results}

All courses include:
- Certificate upon completion
- Expert instruction
- Comprehensive study materials
- Exam preparation support

Would you like to book any of these courses or need more information about a specific one?"""

        # If asking about a course that doesn't exist
        elif any(word in user_lower for word in ["course", "program", "class", "training"]):
            # Check if they mentioned a specific course name that's not in our list
            course_keywords = ["see", "bbs", "bba", "csit", "ielts", "korean", "german", "topik", "eis", "wwis", "nasu", "nimabi", "bridge", "entrance"]
            mentioned_keywords = [word for word in user_lower.split() if word in course_keywords]

            if mentioned_keywords:
                course_results = "\n".join([f"• **{course['name']}** ({course['code']}) - {course['description']}" for course in self.real_courses])
                return f"""I searched for courses related to "{', '.join(mentioned_keywords)}" in our catalog.

Here are our available courses:

{course_results}

We might have courses that cover the topics you're interested in. Would you like more details about any of these courses?"""

        # Default: show all courses
        course_results = "\n".join([f"• **{course['name']}** ({course['code']}) - {course['description']}" for course in self.real_courses])

        return f"""Here are our available courses:

{course_results}

All courses include:
- Certificate upon completion
- Expert instruction
- Comprehensive study materials
- Exam preparation support

Would you like to book any of these courses or need more information about a specific one?"""
    
    def get_memory_stats(self, thread_id: str = "default") -> Dict[str, Any]:
        """Get memory statistics for debugging"""
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            state = self.query_translator.get_state(config)
            messages = state.values.get("messages", [])
            
            return {
                "thread_id": thread_id,
                "message_count": len(messages),
                "last_message": messages[-1].content if messages else None,
                "memory_type": "LangGraph MemorySaver"
            }
        except Exception as e:
            return {
                "thread_id": thread_id,
                "error": str(e)
            }
